"""
AI API转发代理服务器
用于修复缺少<think>标签的AI模型API响应
"""

import json
import os
from typing import Any

import httpx
from fastapi import FastAPI, Request, Response
from fastapi.responses import StreamingResponse


class AIProxy:
    def __init__(self, target_url: str):
        self.target_url = target_url.rstrip("/")
        # 优化httpx客户端配置以提高流式性能
        try:
            # 尝试启用HTTP/2支持
            self.client = httpx.AsyncClient(
                timeout=300.0, limits=httpx.Limits(max_keepalive_connections=20, max_connections=100), http2=True
            )
        except ImportError:
            # 如果没有h2包，则使用HTTP/1.1
            self.client = httpx.AsyncClient(
                timeout=300.0, limits=httpx.Limits(max_keepalive_connections=20, max_connections=100)
            )

    async def forward_request(self, request: Request, path: str) -> Response:
        """转发请求到目标API"""
        # 构建目标URL
        target_url = f"{self.target_url}/{path.lstrip('/')}"
        if request.url.query:
            target_url += f"?{request.url.query}"

        # 获取请求体
        body = await request.body()

        # 准备headers（排除host等）
        headers = dict(request.headers)
        headers.pop("host", None)
        headers.pop("content-length", None)

        # 检查是否可能是流式请求
        is_stream_request = headers.get("accept") == "text/event-stream" or (
            body and b'"stream"' in body and b"true" in body
        )

        print(f"开始发送请求到: {target_url}, 流式请求: {is_stream_request}")

        if is_stream_request:
            # 对于流式请求，使用stream模式以减少延迟
            # 不使用async with，让stream保持打开状态
            response = await self.client.stream(
                method=request.method, url=target_url, headers=headers, content=body
            ).__aenter__()

            print(f"收到流式响应，状态码: {response.status_code}")
            content_type = response.headers.get("content-type", "")

            if "text/event-stream" in content_type or "application/x-ndjson" in content_type:
                print("确认为流式响应")
                # 过滤headers，避免与FastAPI自动添加的headers冲突
                print("开始过滤headers")
                filtered_headers = self._filter_response_headers(dict(response.headers))
                print("headers过滤完成")

                # 创建自定义的StreamingResponse来完全控制headers
                print("开始创建StreamingResponse")

                # 创建一个包装器来处理stream的生命周期
                async def stream_wrapper():
                    try:
                        async for chunk in response.aiter_bytes(chunk_size=1024):
                            yield chunk
                    finally:
                        # 确保stream被正确关闭
                        await response.aclose()

                streaming_response = StreamingResponse(
                    stream_wrapper(),
                    status_code=response.status_code,
                    media_type=content_type,
                )
                print("StreamingResponse创建完成")

                print("流式响应2")
                # 手动设置所有headers以覆盖FastAPI的默认行为
                for key, value in filtered_headers.items():
                    streaming_response.headers[key] = value

                return streaming_response
            else:
                # 不是流式响应，按普通响应处理
                try:
                    content = await response.aread()
                    headers = self._filter_response_headers(dict(response.headers))
                    return Response(content=content, status_code=response.status_code, headers=headers)
                finally:
                    await response.aclose()
        else:
            # 非流式请求，使用普通模式
            response = await self.client.request(
                method=request.method, url=target_url, headers=headers, content=body, follow_redirects=True
            )
            print(f"收到普通响应，状态码: {response.status_code}")

            # 检查是否是流式响应
            content_type = response.headers.get("content-type", "")
            if "text/event-stream" in content_type or "application/x-ndjson" in content_type:
                print("流式响应1")
                # 过滤headers，避免与FastAPI自动添加的headers冲突
                print("开始过滤headers")
                filtered_headers = self._filter_response_headers(dict(response.headers))
                print("headers过滤完成")

                # 创建自定义的StreamingResponse来完全控制headers
                print("开始创建StreamingResponse")
                # 临时测试：直接转发原始流，不做任何处理
                streaming_response = StreamingResponse(
                    response.aiter_bytes(chunk_size=1024),
                    status_code=response.status_code,
                    media_type=content_type,
                )
                print("StreamingResponse创建完成")

                print("流式响应2")
                # 手动设置所有headers以覆盖FastAPI的默认行为
                for key, value in filtered_headers.items():
                    streaming_response.headers[key] = value

                return streaming_response
            else:
                # 非流式响应，直接处理内容
                content = response.content
                headers = self._filter_response_headers(dict(response.headers))

                if content_type.startswith("application/json"):
                    try:
                        data = json.loads(content)
                        modified_data = self._fix_json_response(data)
                        content = json.dumps(modified_data, ensure_ascii=False).encode()
                        # 更新Content-Length
                        headers["content-length"] = str(len(content))
                    except json.JSONDecodeError:
                        pass

                return Response(content=content, status_code=response.status_code, headers=headers)

    async def _process_stream(self, response: httpx.Response):
        """处理流式响应，添加<think>标签"""
        print("_process_stream开始")
        first_content_sent = False

        print("开始aiter_lines循环")
        async for line in response.aiter_lines():
            print(f"收到行: {line[:50]}...")
            # 保持原始格式，包括空行
            if not line.strip():
                yield "\n"
                continue

            # 处理SSE格式
            if line.startswith("data: "):
                data_content = line[6:].strip()
                if data_content == "[DONE]":
                    yield f"{line}\n\n"
                    break

                try:
                    data = json.loads(data_content)
                    # 检查是否需要添加<think>标签
                    if not first_content_sent:
                        data = self._add_think_tag_to_first_chunk(data)
                        if self._has_content(data):
                            first_content_sent = True

                    yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                except json.JSONDecodeError:
                    # 如果解析失败，直接转发原始行
                    yield f"{line}\n\n"
            else:
                # 处理其他SSE字段（如event:, id:等）
                yield f"{line}\n"

    async def _process_stream_fast(self, response: httpx.Response):
        """快速处理流式响应，最小化延迟"""
        print("_process_stream_fast开始")
        first_content_sent = False

        # 直接转发字节流，只在必要时处理<think>标签
        print("开始aiter_bytes循环")
        async for chunk in response.aiter_bytes(chunk_size=1):
            print(f"收到chunk: {len(chunk)}字节")
            # 如果还没有发送第一个内容，尝试检查是否需要添加<think>标签
            if not first_content_sent and chunk:
                # 简单检查：如果看到"content"字段，就认为是第一个内容块
                if b'"content"' in chunk:
                    first_content_sent = True
                    # 这里可以添加<think>标签的逻辑，但为了速度先直接转发

            yield chunk

    def _add_think_tag_to_first_chunk(self, data: dict[str, Any]) -> dict[str, Any]:
        """在第一个内容块添加<think>标签"""
        choices = data.get("choices", [])
        if not choices:
            return data

        choice = choices[0]
        delta = choice.get("delta", {})

        # 检查content字段
        content = delta.get("content", "")
        reasoning_content = delta.get("reasoning_content", "")

        # 如果有内容且不以<think>开头，则添加
        if content and not content.startswith("<think>"):
            delta["content"] = "<think>\n" + content
        elif reasoning_content and not reasoning_content.startswith("<think>"):
            delta["reasoning_content"] = "<think>\n" + reasoning_content

        return data

    def _fix_json_response(self, data: dict[str, Any]) -> dict[str, Any]:
        """修复非流式JSON响应"""
        choices = data.get("choices", [])
        if not choices:
            return data

        choice = choices[0]
        message = choice.get("message", {})
        content = message.get("content", "")

        if content and not content.startswith("<think>"):
            message["content"] = "<think>\n" + content

        return data

    def _has_content(self, data: dict[str, Any]) -> bool:
        """检查数据块是否包含实际内容"""
        choices = data.get("choices", [])
        if not choices:
            return False

        delta = choices[0].get("delta", {})
        return bool(delta.get("content") or delta.get("reasoning_content"))

    def _filter_response_headers(self, headers: dict[str, str]) -> dict[str, str]:
        """过滤响应头，避免与FastAPI自动添加的headers冲突，并保持原生API的header格式"""
        # 标准化header名称映射（保持与原生API一致的大小写）
        header_name_mapping = {
            "content-type": "Content-Type",
            "transfer-encoding": "Transfer-Encoding",
            "connection": "Connection",
            "cache-control": "Cache-Control",
            "x-oneapi-request-id": "X-Oneapi-Request-Id",
            "server": "Server",
            "date": "Date",
        }

        # 标准化所有headers，保持原生API的格式
        filtered = {}
        for key, value in headers.items():
            key_lower = key.lower()
            # 使用标准化的header名称，如果没有映射则保持原样
            standard_key = header_name_mapping.get(key_lower, key)
            filtered[standard_key] = value

        return filtered


def create_app(target_url: str | None = None) -> FastAPI:
    """创建FastAPI应用"""
    if not target_url:
        target_url = os.getenv("TARGET_API_URL", "https://api.deepseek.com/v1")

    app = FastAPI(title="AI API Proxy", description="转发AI模型API并修复<think>标签")
    proxy = AIProxy(target_url)

    @app.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"])
    async def proxy_request(request: Request, path: str):  # type: ignore
        """代理所有请求"""
        return await proxy.forward_request(request, path)

    return app
